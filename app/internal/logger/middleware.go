package logger

import (
	"context"
	"log/slog"
	"net/http"
	"strings"
	"time"

	"github.com/go-chi/chi/v5/middleware"
)

// MiddlewareConfig holds configuration for the HTTP logging middleware
type MiddlewareConfig struct {
	// SkipPaths contains URL path prefixes that should not be logged
	SkipPaths []string
	// ComponentMapping maps URL path prefixes to component names for logging
	ComponentMapping map[string]string
	// DefaultComponent is used when no mapping matches
	DefaultComponent string
}

// DefaultMiddlewareConfig returns a sensible default configuration
func DefaultMiddlewareConfig() MiddlewareConfig {
	return MiddlewareConfig{
		SkipPaths: []string{"/health/"},
		ComponentMapping: map[string]string{
			"/api/":    "api",
			"/oauth/":  "oauth",
			"/admin/":  "admin",
			"/ui-api/": "ui-api",
			"/static/": "static",
		},
		DefaultComponent: "ui",
	}
}

// RequestLogging returns a middleware that logs HTTP requests with configurable behavior
func RequestLogging(logger *slog.Logger) func(http.Handler) http.Handler {
	return RequestLoggingWithConfig(logger, DefaultMiddlewareConfig())
}

// RequestLoggingWithConfig returns a middleware that logs HTTP requests with custom configuration
func RequestLoggingWithConfig(logger *slog.Logger, config MiddlewareConfig) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Skip logging for configured paths
			for _, skipPath := range config.SkipPaths {
				if strings.HasPrefix(r.URL.Path, skipPath) {
					next.ServeHTTP(w, r)
					return
				}
			}

			start := time.Now()
			requestID := middleware.GetReqID(r.Context())

			// Determine request component based on path mapping
			component := config.DefaultComponent
			for pathPrefix, componentName := range config.ComponentMapping {
				if strings.HasPrefix(r.URL.Path, pathPrefix) {
					component = componentName
					break
				}
			}

			// Create request-scoped logger with common fields using structured attributes
			reqLogger := logger.With(
				slog.String("request_id", requestID),
				slog.String("method", r.Method),
				slog.String("path", r.URL.Path),
				slog.String("remote_addr", r.RemoteAddr),
				slog.String("component", component),
			)

			// Add logger to context for handlers to use
			ctx := context.WithValue(r.Context(), loggerContextKey, reqLogger)

			// Wrap response writer to capture status
			ww := middleware.NewWrapResponseWriter(w, r.ProtoMajor)

			next.ServeHTTP(ww, r.WithContext(ctx))

			duration := time.Since(start)

			// Log completion with appropriate level using structured attributes
			attrs := []slog.Attr{
				slog.Int("status", ww.Status()),
				slog.Duration("duration", duration),
				slog.Int("bytes", ww.BytesWritten()),
			}

			switch {
			case ww.Status() >= 500:
				reqLogger.LogAttrs(context.Background(), slog.LevelError, "Request completed", attrs...)
			case ww.Status() >= 400:
				reqLogger.LogAttrs(context.Background(), slog.LevelWarn, "Request completed", attrs...)
			default:
				reqLogger.LogAttrs(context.Background(), slog.LevelInfo, "Request completed", attrs...)
			}
		})
	}
}
