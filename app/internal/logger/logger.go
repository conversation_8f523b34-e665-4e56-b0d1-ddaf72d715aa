package logger

import (
	"context"
	"io"
	"log/slog"
	"os"
	"time"
)

// create a logger
// - For dev environment, uses text handler with stderr
// - For production, uses JSON handler with stdout
func New(level slog.Level, environment string) *slog.Logger {
	opts := &slog.HandlerOptions{
		Level:     level,
		AddSource: environment == "dev",
	}

	var handler slog.Handler
	if environment == "dev" {
		handler = slog.NewTextHandler(os.Stderr, opts)
	} else {
		handler = slog.NewJSONHandler(os.Stdout, opts)
	}

	return slog.New(handler)
}

// todo
// NewWithOutput creates a logger with custom output
func NewWithOutput(level slog.Level, environment string, output io.Writer) *slog.Logger {
	opts := &slog.HandlerOptions{
		Level:     level,
		AddSource: environment == "dev",
	}

	var handler slog.Handler
	if environment == "dev" {
		handler = slog.NewTextHandler(output, opts)
	} else {
		handler = slog.NewJSONHandler(output, opts)
	}

	return slog.New(handler)
}

// Context key type to avoid collisions
type contextKey string

const loggerContextKey contextKey = "logger"

// FromContext retrieves the request-scoped logger from context
// Falls back to the provided default logger if none found
func FromContext(ctx context.Context, defaultLogger *slog.Logger) *slog.Logger {
	if logger, ok := ctx.Value(loggerContextKey).(*slog.Logger); ok {
		return logger
	}
	return defaultLogger
}

// Common attribute helpers for consistent logging
func ErrorAttr(err error) slog.Attr {
	return slog.Any("error", err)
}

func StatusAttr(status int) slog.Attr {
	return slog.Int("status", status)
}

func PathAttr(path string) slog.Attr {
	return slog.String("path", path)
}

func MethodAttr(method string) slog.Attr {
	return slog.String("method", method)
}

func DurationAttr(duration time.Duration) slog.Attr {
	return slog.Duration("duration", duration)
}

func UserAttr(userID string) slog.Attr {
	return slog.String("user_id", userID)
}

func ComponentAttr(component string) slog.Attr {
	return slog.String("component", component)
}

func CookieValueAttr(value string) slog.Attr {
	return slog.String("cookie_value", value)
}

func JSONAttr(json string) slog.Attr {
	return slog.String("json", json)
}

func AccountIDAttr(accountID string) slog.Attr {
	return slog.String("account_id", accountID)
}

// HTTP-specific logging helpers
func LogHTTPError(logger *slog.Logger, msg string, err error, status int, path string) {
	logger.Error(msg, ErrorAttr(err), StatusAttr(status), PathAttr(path))
}

func LogHTTPRequest(logger *slog.Logger, msg string, method, path string, status int, duration time.Duration) {
	logger.Info(msg,
		MethodAttr(method),
		PathAttr(path),
		StatusAttr(status),
		DurationAttr(duration),
	)
}

// todo
// Backward compatibility functions
func InitServerLogger() *slog.Logger {
	return New(slog.LevelDebug, "dev")
}

func InitHttpLogger(logLevel slog.Level, environment string) *slog.Logger {
	return New(logLevel, environment)
}
