package appcontext

import (
	"context"
	"log/slog"

	"github.com/google/uuid"
	"github.com/information-sharing-networks/signalsd/app/internal/auth"
)

// Common context keys - use a struct to prevent conflicts
type contextKey struct {
	name string
}

var (
	accountIDKey          = contextKey{"account-id"}
	accountTypeKey        = contextKey{"account-type"}
	claimsKey             = contextKey{"claims"}
	hashedRefreshTokenKey = contextKey{"hashed_refresh_token"}
	loggerContextKey      = contextKey{"logger"}
)

func ContextWithAccountID(ctx context.Context, id uuid.UUID) context.Context {
	return context.WithValue(ctx, accountIDKey, id)
}

func ContextAccountID(ctx context.Context) (uuid.UUID, bool) {
	id, ok := ctx.Value(accountIDKey).(uuid.UUID)
	return id, ok
}

func ContextWithAccountType(ctx context.Context, accountType string) context.Context {
	return context.WithValue(ctx, accountTypeKey, accountType)
}

func ContextAccountType(ctx context.Context) (string, bool) {
	accountType, ok := ctx.Value(accountTypeKey).(string)
	return accountType, ok
}

func ContextWithHashedRefreshToken(ctx context.Context, token string) context.Context {
	return context.WithValue(ctx, hashedRefreshTokenKey, token)
}

func ContextHashedRefreshToken(ctx context.Context) (string, bool) {
	token, ok := ctx.Value(hashedRefreshTokenKey).(string)
	return token, ok
}

func ContextWithClaims(ctx context.Context, claims *auth.Claims) context.Context {
	return context.WithValue(ctx, claimsKey, claims)
}

func ContextClaims(ctx context.Context) (*auth.Claims, bool) {
	claims, ok := ctx.Value(claimsKey).(*auth.Claims)
	return claims, ok
}

func ContextWithLogger(ctx context.Context, logger *slog.Logger) context.Context {
	return context.WithValue(ctx, loggerContextKey, logger)
}

func ContextLogger(ctx context.Context) (*slog.Logger, bool) {
	logger, ok := ctx.Value(loggerContextKey).(*slog.Logger)
	return logger, ok
}
