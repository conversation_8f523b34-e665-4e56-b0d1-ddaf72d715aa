package responses

import (
	"encoding/json"
	"log/slog"
	"net/http"

	"github.com/go-chi/chi/v5/middleware"
	"github.com/information-sharing-networks/signalsd/app/internal/appcontext"
	"github.com/information-sharing-networks/signalsd/app/internal/apperrors"
)

type ErrorResponse struct {
	StatusCode int                 `json:"-"`
	ErrorCode  apperrors.ErrorCode `json:"error_code" example:"example_error_code"`
	Message    string              `json:"message" example:"message describing the error"`
	ReqID      string              `json:"-"`
}

func RespondWithError(w http.ResponseWriter, r *http.Request, statusCode int, errorCode apperrors.ErrorCode, message string) {
	// Get logger from context, fall back to a default logger if not found
	//todo
	//requestLogger := logger.FromContext(r.Context(), slog.Default())

	requestLogger, ok := appcontext.ContextLogger(r.Context())
	if !ok {
		requestLogger = slog.Default()
		requestLogger.Error("logger not found in context")
	}

	requestID := middleware.GetReqID(r.Context())

	// Log with appropriate level
	logArgs := []any{
		slog.Int("status", statusCode),
		slog.Any("error_code", errorCode),
		slog.String("error_message", message),
		slog.String("request_id", requestID),
	}

	switch {
	case statusCode >= 500:
		requestLogger.Error("Request failed", logArgs...)
	case statusCode >= 400:
		requestLogger.Warn("Request failed", logArgs...)
	default:
		requestLogger.Info("Request failed", logArgs...)
	}

	errResponse := ErrorResponse{
		StatusCode: statusCode,
		ErrorCode:  errorCode,
		Message:    message,
		ReqID:      requestID,
	}

	dat, err := json.Marshal(errResponse)
	if err != nil {
		requestLogger.Error("error marshaling error response", "error", err)
		w.WriteHeader(http.StatusInternalServerError)
		_, _ = w.Write([]byte(`{"error_code":"internal_error","message":"Internal Server Error"}`))
		return
	}

	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.WriteHeader(statusCode)
	_, _ = w.Write(dat)
}

func RespondWithJSON(w http.ResponseWriter, status int, payload any) {
	if status == http.StatusNoContent {
		w.WriteHeader(status)
		return
	}

	data, err := json.Marshal(payload)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		_, _ = w.Write([]byte(`{"error_code":"marshal_error","message":"Internal Server Error"}`))
		return
	}

	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.WriteHeader(status)
	_, _ = w.Write(data)
}

func RespondWithStatusCodeOnly(w http.ResponseWriter, status int) {
	w.WriteHeader(status)
}
