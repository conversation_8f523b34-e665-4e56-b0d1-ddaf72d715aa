package main

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"os/signal"
	"syscall"

	"github.com/information-sharing-networks/signalsd/app/internal/logger"
	"github.com/information-sharing-networks/signalsd/app/internal/ui"
	"github.com/information-sharing-networks/signalsd/app/internal/version"
	"github.com/spf13/cobra"
)

func main() {
	cmd := &cobra.Command{
		Use:   "signalsd-ui",
		Short: "Signalsd web user interface",
		Long:  `Web UI for managing Information Sharing Networks`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return run()
		},
	}

	v := version.Get()
	cmd.Version = fmt.Sprintf("%s (built %s, commit %s)", v.Version, v.BuildDate, v.GitCommit)

	if err := cmd.Execute(); err != nil {
		os.Exit(1)
	}
}

func run() error {
	// Use temporary logger for initial config loading
	tempLogger := logger.InitServerLogger()

	// Load UI configuration
	cfg, err := ui.NewConfig(tempLogger)
	if err != nil {
		tempLogger.Error("Failed to load UI configuration", "error", err)
		os.Exit(1)
	}

	// Create loggers
	serverLogger := logger.New(slog.LevelDebug, cfg.Environment)
	uiLogger := logger.New(cfg.LogLevel, cfg.Environment)

	serverLogger.Info("Starting UI server", "version", version.Get().Version)
	serverLogger.Info("using signalsd API", "url", cfg.APIBaseURL)

	// Create UI server
	server := ui.NewStandaloneServer(cfg, uiLogger)

	// Set up graceful shutdown handling
	ctx, stop := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM)
	defer stop()

	// Run the server
	if err := server.Start(ctx); err != nil {
		serverLogger.Error("UI server error", "error", err)
		return err
	}

	serverLogger.Info("UI server shutdown complete")
	return nil
}
